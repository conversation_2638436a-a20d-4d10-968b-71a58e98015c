import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:balshifa/models/dose.dart';
import 'package:balshifa/utils/time_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // للتحكم في عرض الإشعارات على الشاشة
  static BuildContext? _currentContext;

  Future<void> initialize() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
    );
  }

  void onDidReceiveNotificationResponse(NotificationResponse details) {
    // معالجة الضغط على الإشعارات وأزرار الإجراءات
    print('تم الضغط على الإشعار: ${details.payload}');
    print('الإجراء: ${details.actionId}');

    if (details.actionId == 'taken') {
      // تم تناول الدواء - إلغاء الإشعار وإيقاف الصوت
      final notificationId = _extractNotificationId(details.payload);
      if (notificationId != null) {
        cancelNotification(notificationId);
        _stopNotificationActivity();
      }
    } else if (details.actionId == 'later') {
      // تذكير لاحقاً - إيقاف الصوت مؤقتاً
      _stopNotificationActivity();
      // يمكن إضافة منطق لإعادة التذكير بعد فترة
    }
  }

  int? _extractNotificationId(String? payload) {
    if (payload != null && payload.startsWith('dose_')) {
      try {
        return int.parse(payload.substring(5));
      } catch (e) {
        print('خطأ في استخراج ID الإشعار: $e');
      }
    }
    return null;
  }

  Future<void> _stopNotificationActivity() async {
    try {
      const platform = MethodChannel('notification_channel');
      await platform.invokeMethod('stopNotificationActivity');
    } catch (e) {
      print('خطأ في إيقاف النشاط: $e');
    }
  }

  Future<void> showDoseNotification(Dose dose, String medicineName) async {
    final timeString = TimeFormatter.formatTime12Hour(dose.scheduledDateTime);
    final notificationId = dose.id ?? 0;

    // إنشاء إشعار صامت (بدون صوت) في شريط الإشعارات
    const AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      'dose_reminder_channel',
      'تذكير بالجرعات',
      channelDescription: 'تذكير بمواعيد الجرعات الدوائية',
      importance: Importance.max,
      priority: Priority.high,
      ongoing: true, // يجعل الإشعار دائمًا
      autoCancel: false,
      playSound: false, // بدون صوت هنا - الصوت سيكون في النافذة المنبثقة
      actions: <AndroidNotificationAction>[
        AndroidNotificationAction(
          'taken',
          'تم التناول ✅',
          showsUserInterface: true,
        ),
        AndroidNotificationAction(
          'later',
          'تذكيرني لاحقاً ⏰',
          showsUserInterface: true,
        ),
      ],
    );

    const NotificationDetails notificationDetails =
        NotificationDetails(android: androidNotificationDetails);

    // إشعار في شريط الإشعارات
    await _notificationsPlugin.show(
      notificationId,
      'وقت الجرعة ⏰',
      'الدواء: $medicineName\nالوقت: $timeString',
      notificationDetails,
      payload: 'dose_${dose.id}',
    );

    // إظهار النافذة المنبثقة مع الصوت المستمر
    await _showFullScreenNotification(medicineName, timeString, notificationId);
  }

  Future<void> cancelNotification(int notificationId) async {
    await _notificationsPlugin.cancel(notificationId);
  }

  Future<void> cancelAllNotifications() async {
    await _notificationsPlugin.cancelAll();
  }

  // تعيين السياق الحالي للتطبيق
  static void setContext(BuildContext context) {
    _currentContext = context;
  }

  // إظهار النافذة المنبثقة باستخدام Android Activity
  Future<void> _showFullScreenNotification(String medicineName, String time, int notificationId) async {
    try {
      const platform = MethodChannel('notification_channel');
      await platform.invokeMethod('showNotificationActivity', {
        'medicine_name': medicineName,
        'dose_time': time,
        'notification_id': notificationId,
      });
    } catch (e) {
      print('خطأ في عرض النافذة المنبثقة: $e');
      // fallback للطريقة القديمة إذا فشلت
      _showOnScreenNotificationFallback(medicineName, time);
    }
  }

  // النافذة المنبثقة الاحتياطية (داخل التطبيق فقط)
  void _showOnScreenNotificationFallback(String medicineName, String time) {
    if (_currentContext != null) {
      showDialog(
        context: _currentContext!,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.alarm, color: Colors.red, size: 30),
              SizedBox(width: 10),
              Text('⏰ وقت الجرعة!'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'حان وقت تناول الدواء',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10),
              Text('الدواء: $medicineName'),
              Text('الوقت: $time'),
              SizedBox(height: 20),
              Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Text(
                  '⚠️ لا تنس تناول الدواء في الوقت المحدد',
                  style: TextStyle(color: Colors.red.shade700),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // إلغاء الإشعار من شريط الإشعارات أيضاً
                cancelNotification(0); // سنحتاج لتمرير ID صحيح
              },
              child: Text('تم التناول ✅'),
              style: TextButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('تذكيرني لاحقاً ⏰'),
              style: TextButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }
  }
}
