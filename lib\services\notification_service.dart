import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:balshifa/models/dose.dart';
import 'package:flutter/material.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // للتحكم في عرض الإشعارات على الشاشة
  static BuildContext? _currentContext;

  Future<void> initialize() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
    );
  }

  void onDidReceiveNotificationResponse(NotificationResponse details) {
    // معالجة الضغط على الإشعارات
    print('تم الضغط على الإشعار: ${details.payload}');
  }

  Future<void> showDoseNotification(Dose dose, String medicineName) async {
    const AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      'dose_reminder_channel',
      'تذكير بالجرعات',
      channelDescription: 'تذكير بمواعيد الجرعات الدوائية',
      importance: Importance.max,
      priority: Priority.high,
      ongoing: true, // يجعل الإشعار دائمًا
      autoCancel: false,
      playSound: true, // تشغيل الصوت
      sound: RawResourceAndroidNotificationSound('notification'), // اسم ملف الصوت
    );

    const NotificationDetails notificationDetails =
        NotificationDetails(android: androidNotificationDetails);

    // إشعار في شريط الإشعارات
    await _notificationsPlugin.show(
      dose.id ?? 0,
      'وقت الجرعة ⏰',
      'الدواء: $medicineName\nالجرعة: ${dose.scheduledDateTime.hour}:${dose.scheduledDateTime.minute.toString().padLeft(2, '0')}',
      notificationDetails,
      payload: '${dose.id}',
    );

    // إشعار على الشاشة (إذا كان التطبيق مفتوحاً)
    final timeString = '${dose.scheduledDateTime.hour}:${dose.scheduledDateTime.minute.toString().padLeft(2, '0')}';
    showOnScreenNotification(medicineName, timeString);
  }

  Future<void> cancelNotification(int notificationId) async {
    await _notificationsPlugin.cancel(notificationId);
  }

  Future<void> cancelAllNotifications() async {
    await _notificationsPlugin.cancelAll();
  }

  // تعيين السياق الحالي للتطبيق
  static void setContext(BuildContext context) {
    _currentContext = context;
  }

  // عرض إشعار على الشاشة مباشرة
  static void showOnScreenNotification(String medicineName, String time) {
    if (_currentContext != null) {
      showDialog(
        context: _currentContext!,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.alarm, color: Colors.red, size: 30),
              SizedBox(width: 10),
              Text('⏰ وقت الجرعة!'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'حان وقت تناول الدواء',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10),
              Text('الدواء: $medicineName'),
              Text('الوقت: $time'),
              SizedBox(height: 20),
              Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Text(
                  '⚠️ لا تنس تناول الدواء في الوقت المحدد',
                  style: TextStyle(color: Colors.red.shade700),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('تم التناول ✅'),
              style: TextButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('تذكيرني لاحقاً ⏰'),
              style: TextButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }
  }
}
