import 'package:balshifa/models/dose.dart';
import 'package:balshifa/models/medicine.dart';
import 'package:balshifa/models/patient.dart';
import 'package:balshifa/screens/add_medicine_screen.dart';
import 'package:balshifa/screens/dose_history_screen.dart';
import 'package:balshifa/screens/edit_medicine_screen.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/services/notification_service.dart';
import 'package:balshifa/utils/dose_scheduler.dart';
import 'package:balshifa/constants/app_colors.dart';
import 'package:balshifa/constants/app_styles.dart';
import 'package:flutter/material.dart';

class PatientScreen extends StatefulWidget {
  final Patient patient;

  const PatientScreen({super.key, required this.patient});

  @override
  State<PatientScreen> createState() => _PatientScreenState();
}

class _PatientScreenState extends State<PatientScreen> {
  List<Medicine> medicines = [];

  @override
  void initState() {
    super.initState();
    _loadMedicines();
  }

  _loadMedicines() async {
    final loadedMedicines =
        await DatabaseService.instance.getMedicinesByPatient(widget.patient.id!);
    setState(() {
      medicines = loadedMedicines;
    });
  }

  _addMedicine() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddMedicineScreen(patientId: widget.patient.id!),
      ),
    );

    if (result == true) {
      _loadMedicines();
    }
  }

  _deleteMedicine(Medicine medicine) async {
    await DatabaseService.instance.deleteMedicine(medicine.id!);
    // حفظ اسم الدواء للاستخدام المستقبلي
    await DatabaseService.instance.saveMedicineName(medicine.name);
    _loadMedicines();
  }

  _editMedicine(Medicine medicine) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditMedicineScreen(medicine: medicine),
      ),
    );

    if (result == true) {
      _loadMedicines();
    }
  }

  _showDoseConfirmationDialog(Medicine medicine) async {
    // الحصول على الجرعات المجدولة لهذا الدواء
    final scheduledDoses = await DatabaseService.instance.getScheduledDoses(medicine.id!);

    if (scheduledDoses.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لا توجد جرعات مجدولة حالياً')),
        );
      }
      return;
    }

    // البحث عن الجرعة الأقرب في الوقت
    Dose? nearestDose;
    DateTime now = DateTime.now();
    Duration? minDifference;

    for (final dose in scheduledDoses) {
      final difference = dose.scheduledDateTime.difference(now).abs();
      if (minDifference == null || difference < minDifference) {
        minDifference = difference;
        nearestDose = dose;
      }
    }

    if (nearestDose != null) {
      final dose = nearestDose!; // تأكيد أن nearestDose ليس null
      if (mounted) {
        await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تأكيد الجرعة'),
            content: Text(
              'هل قمت بأخذ جرعة ${medicine.name} الآن؟\n'
              'الوقت المجدول: ${_formatTime(dose.scheduledDateTime!)}',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  // تحديث الجرعة إلى "تم أخذها"
                  final takenDose = Dose(
                    id: dose.id,
                    medicineId: dose.medicineId,
                    scheduledDateTime: dose.scheduledDateTime,
                    takenDateTime: DateTime.now(),
                    status: 'taken',
                  );

                  await DatabaseService.instance.updateDose(takenDose);

                  if (mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم تسجيل الجرعة')),
                    );
                  }
                },
                child: const Text('نعم، تم أخذها'),
              ),
            ],
          ),
        );
      }
    }
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.patient.name),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DoseHistoryScreen(
                    patientId: widget.patient.id!,
                    patientName: widget.patient.name,
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الأدوية (${medicines.length})',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addMedicine,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة دواء'),
                  style: AppStyles.primaryButton,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: medicines.isEmpty
                  ? const Center(
                      child: Text(
                        'لا توجد أدوية مضافه',
                        style: TextStyle(fontSize: 18),
                      ),
                    )
                  : ListView.builder(
                      itemCount: medicines.length,
                      itemBuilder: (context, index) {
                        final medicine = medicines[index];
                        return _buildMedicineCard(medicine);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMedicineCard(Medicine medicine) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    medicine.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.check, color: AppColors.success),
                      onPressed: () => _showDoseConfirmationDialog(medicine),
                    ),
                    IconButton(
                      icon: const Icon(Icons.edit, color: AppColors.primary),
                      onPressed: () => _editMedicine(medicine),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: AppColors.error),
                      onPressed: () => _deleteMedicine(medicine),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.category, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(medicine.type),
                const SizedBox(width: 16),
                const Icon(Icons.medication, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(medicine.dosage),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.access_time, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text('${medicine.timesPerDay} مرات يوميًا'),
              ],
            ),
            if (medicine.durationDays != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.calendar_today,
                      size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text('لمدة ${medicine.durationDays} أيام'),
                ],
              ),
            ],
            const SizedBox(height: 8),
            Text(
              'الجرعة الأولى: ${_formatDateTime(medicine.firstDoseDateTime)}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
