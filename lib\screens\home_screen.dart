import 'package:balshifa/models/patient.dart';
import 'package:balshifa/models/dose.dart';
import 'package:balshifa/screens/patient_screen.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/services/notification_service.dart';
import 'package:balshifa/widgets/patient_tile.dart';
import 'package:balshifa/constants/app_colors.dart';
import 'package:balshifa/constants/app_styles.dart';
import 'package:flutter/material.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<Patient> patients = [];

  @override
  void initState() {
    super.initState();
    _loadPatients();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تعيين السياق للإشعارات
    NotificationService.setContext(context);
  }

  _loadPatients() async {
    final loadedPatients = await DatabaseService.instance.getAllPatients();
    setState(() {
      patients = loadedPatients;
    });
  }

  _addPatient() async {
    String patientName = '';
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة مريض جديد'),
        content: TextField(
          onChanged: (value) => patientName = value,
          decoration: const InputDecoration(hintText: 'اسم المريض'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              if (patientName.trim().isNotEmpty) {
                final patient = Patient(name: patientName.trim());
                await DatabaseService.instance.insertPatient(patient);
                _loadPatients();
                Navigator.pop(context);
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  _testNotification() async {
    // إنشاء جرعة تجريبية لاختبار الإشعار
    final testDose = Dose(
      id: 999,
      medicineId: 1,
      scheduledDateTime: DateTime.now(),
      status: 'scheduled',
    );

    // عرض الإشعار
    await NotificationService().showDoseNotification(testDose, 'دواء تجريبي');

    // عرض رسالة تأكيد
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إرسال إشعار تجريبي! تحقق من شريط الإشعارات'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('بالشفا'),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _testNotification,
            icon: const Icon(Icons.notifications_active),
            tooltip: 'اختبار الإشعارات',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: patients.isEmpty
                  ? const Center(
                      child: Text(
                        'لا توجد مرضى مضافين',
                        style: TextStyle(fontSize: 18),
                      ),
                    )
                  : ListView.builder(
                      itemCount: patients.length,
                      itemBuilder: (context, index) {
                        final patient = patients[index];
                        return PatientTile(
                          patient: patient,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PatientScreen(
                                  patient: patient,
                                ),
                              ),
                            ).then((_) => _loadPatients());
                          },
                          onDelete: () async {
                            await DatabaseService.instance
                                .deletePatient(patient.id!);
                            _loadPatients();
                          },
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addPatient,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }
}
