import 'package:balshifa/models/dose.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/constants/app_colors.dart';
import 'package:flutter/material.dart';

class DoseHistoryScreen extends StatefulWidget {
  final int patientId;
  final String patientName;

  const DoseHistoryScreen({
    super.key,
    required this.patientId,
    required this.patientName,
  });

  @override
  State<DoseHistoryScreen> createState() => _DoseHistoryScreenState();
}

class _DoseHistoryScreenState extends State<DoseHistoryScreen> {
  List<Dose> doses = [];

  @override
  void initState() {
    super.initState();
    _loadDoseHistory();
  }

  _loadDoseHistory() async {
    final loadedDoses =
        await DatabaseService.instance.getDoseHistory(widget.patientId);
    setState(() {
      doses = loadedDoses;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('سجل الجرعات - ${widget.patientName}'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: doses.isEmpty
            ? const Center(
                child: Text(
                  'لا توجد جرعات مسجلة',
                  style: TextStyle(fontSize: 18),
                ),
              )
            : ListView.builder(
                itemCount: doses.length,
                itemBuilder: (context, index) {
                  final dose = doses[index];
                  return _buildDoseTile(dose);
                },
              ),
      ),
    );
  }

  Widget _buildDoseTile(Dose dose) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(
          dose.status == 'taken'
              ? Icons.check_circle
              : dose.status == 'missed'
                  ? Icons.cancel
                  : Icons.access_time,
          color: dose.status == 'taken'
              ? AppColors.success
              : dose.status == 'missed'
                  ? AppColors.error
                  : AppColors.warning,
        ),
        title: Text(
          'الجرعة: ${_formatDateTime(dose.scheduledDateTime)}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الحالة: ${_getStatusText(dose.status)}'),
            if (dose.takenDateTime != null)
              Text('تم أخذها: ${_formatDateTime(dose.takenDateTime!)}'),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'taken':
        return 'تم أخذها';
      case 'missed':
        return 'لم تؤخذ';
      case 'scheduled':
        return 'مجدولة';
      default:
        return status;
    }
  }
}
