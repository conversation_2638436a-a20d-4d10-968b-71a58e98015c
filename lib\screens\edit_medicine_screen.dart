import 'package:balshifa/models/medicine.dart';
import 'package:balshifa/services/database_service.dart';
import 'package:balshifa/utils/dose_scheduler.dart';
import 'package:balshifa/constants/app_colors.dart';
import 'package:balshifa/constants/app_styles.dart';
import 'package:flutter/material.dart';

class EditMedicineScreen extends StatefulWidget {
  final Medicine medicine;

  const EditMedicineScreen({super.key, required this.medicine});

  @override
  State<EditMedicineScreen> createState() => _EditMedicineScreenState();
}

class _EditMedicineScreenState extends State<EditMedicineScreen> {
  final _formKey = GlobalKey<FormState>();
  
  late TextEditingController _nameController;
  late TextEditingController _dosageController;
  late TextEditingController _timesPerDayController;
  late TextEditingController _durationController;
  
  late String _selectedType;
  late DateTime _firstDoseDateTime;
  
  List<String> medicineTypes = [
    'مسكن',
    'مضاد حيوي',
    'خافض حرارة',
    'مكمل غذائي',
    'مضاد التهاب',
    'مضاد حموضة',
    'مهدئ',
    'فيتامين',
    'مضاد فيروسي',
    'مضاد فطري',
  ];
  
  List<String> savedMedicineNames = [];
  List<String> filteredNames = [];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadSavedMedicineNames();
  }

  _initializeControllers() {
    _nameController = TextEditingController(text: widget.medicine.name);
    _dosageController = TextEditingController(text: widget.medicine.dosage);
    _timesPerDayController = TextEditingController(text: widget.medicine.timesPerDay.toString());
    _durationController = TextEditingController(
      text: widget.medicine.durationDays?.toString() ?? ''
    );
    _selectedType = widget.medicine.type;
    _firstDoseDateTime = widget.medicine.firstDoseDateTime;
  }

  _loadSavedMedicineNames() async {
    final names = await DatabaseService.instance.getSavedMedicineNames();
    setState(() {
      savedMedicineNames = names;
      filteredNames = names;
    });
  }

  _filterNames(String query) {
    if (query.isEmpty) {
      setState(() {
        filteredNames = savedMedicineNames;
      });
    } else {
      final filtered = savedMedicineNames
          .where((name) => name.toLowerCase().contains(query.toLowerCase()))
          .toList();
      setState(() {
        filteredNames = filtered;
      });
    }
  }

  _updateMedicine() async {
    if (_formKey.currentState!.validate()) {
      final updatedMedicine = Medicine(
        id: widget.medicine.id,
        patientId: widget.medicine.patientId,
        name: _nameController.text.trim(),
        type: _selectedType,
        dosage: _dosageController.text.trim(),
        timesPerDay: int.parse(_timesPerDayController.text),
        durationDays: _durationController.text.isEmpty
            ? null
            : int.parse(_durationController.text),
        firstDoseDateTime: _firstDoseDateTime,
      );

      // تحديث الدواء في قاعدة البيانات
      final db = DatabaseService.instance;
      await db.updateMedicine(updatedMedicine);
      
      // حفظ اسم الدواء للاستخدام المستقبلي
      await DatabaseService.instance.saveMedicineName(updatedMedicine.name);
      
      // إعادة جدولة الجرعات
      // أولاً حذف الجرعات القديمة
      await db.deleteDosesByMedicine(widget.medicine.id!);
      // ثم جدولة الجرعات الجديدة
      await DoseScheduler.scheduleDoses(updatedMedicine);
      
      if (mounted) {
        Navigator.pop(context, true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل الدواء'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              // اسم الدواء
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الدواء',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.medication),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم الدواء';
                  }
                  return null;
                },
                onChanged: _filterNames,
              ),
              
              // قائمة الاقتراحات
              if (filteredNames.isNotEmpty && _nameController.text.isNotEmpty)
                Container(
                  height: 150,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: ListView.builder(
                    itemCount: filteredNames.length,
                    itemBuilder: (context, index) {
                      return ListTile(
                        title: Text(filteredNames[index]),
                        onTap: () {
                          setState(() {
                            _nameController.text = filteredNames[index];
                            filteredNames = [];
                          });
                        },
                      );
                    },
                  ),
                ),
              
              const SizedBox(height: 16),
              
              // نوع الدواء
              DropdownButtonFormField<String>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع الدواء',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category),
                ),
                items: medicineTypes.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              
              const SizedBox(height: 16),
              
              // الجرعة
              TextFormField(
                controller: _dosageController,
                decoration: const InputDecoration(
                  labelText: 'الجرعة (مثال: 5 سم - قرص - نصف قرص)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.scale),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال الجرعة';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // عدد مرات الجرعة في اليوم
              TextFormField(
                controller: _timesPerDayController,
                decoration: const InputDecoration(
                  labelText: 'عدد مرات الجرعة في اليوم (1-6)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.repeat),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال عدد مرات الجرعة';
                  }
                  final times = int.tryParse(value);
                  if (times == null || times < 1 || times > 6) {
                    return 'يرجى إدخال رقم بين 1 و 6';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // مدة العلاج
              TextFormField(
                controller: _durationController,
                decoration: const InputDecoration(
                  labelText: 'مدة العلاج (بالأيام) - اختياري',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                keyboardType: TextInputType.number,
              ),

              const SizedBox(height: 16),

              // تاريخ ووقت أول جرعة
              ListTile(
                title: const Text('تاريخ ووقت أول جرعة'),
                subtitle: Text(_formatDateTime(_firstDoseDateTime)),
                trailing: const Icon(Icons.edit),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _firstDoseDateTime,
                    firstDate: DateTime(2020),
                    lastDate: DateTime(2030),
                  );

                  if (date != null) {
                    final time = await showTimePicker(
                      context: context,
                      initialTime: TimeOfDay.fromDateTime(_firstDoseDateTime),
                    );

                    if (time != null) {
                      setState(() {
                        _firstDoseDateTime = DateTime(
                          date.year,
                          date.month,
                          date.day,
                          time.hour,
                          time.minute,
                        );
                      });
                    }
                  }
                },
              ),

              const SizedBox(height: 32),

              // زر الحفظ
              ElevatedButton(
                onPressed: _updateMedicine,
                style: AppStyles.primaryButton.copyWith(
                  padding: MaterialStateProperty.all(
                    const EdgeInsets.symmetric(vertical: 16),
                  ),
                  textStyle: MaterialStateProperty.all(
                    const TextStyle(fontSize: 18),
                  ),
                ),
                child: const Text('تحديث الدواء وإعادة الجدولة'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _dosageController.dispose();
    _timesPerDayController.dispose();
    _durationController.dispose();
    super.dispose();
  }
}
